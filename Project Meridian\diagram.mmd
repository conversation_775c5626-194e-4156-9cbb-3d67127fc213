---
title: <PERSON>. Conceptual Diagram - Project Meridian Ecosystem
---
graph TD
    subgraph Z1 [Zone 1: Offshore Assets]
        direction LR
        Vessel["<img src='https://img.icons8.com/ios-filled/50/000000/cargo-ship.png' width='40' /> Survey Vessel <br/> (AUV/ROV Capable)"]
        Vessel --> EdgeSys{"Onboard Sensor Suite <br/> & Edge Processing <br/> (Ignition Edge & AI Modules)"}
        EdgeSys -.->|"Data Acquisition, <br/> Local Control & Intelligence"| Vessel
    end

    subgraph Z2 [Zone 2: Communication Link]
        direction TB
        CommsLink["<img src='https://img.icons8.com/ios/50/000000/satellite-dish.png' width='30' /> <img src='https://img.icons8.com/ios/50/000000/cell-tower.png' width='30' /> <img src='https://img.icons8.com/ios/50/000000/cloud.png' width='30' /> <br/> High-Bandwidth, Resilient Data Pipeline"]
    end

    subgraph Z3 [Zone 3: Shore-Side Remote Operations Center (ROC)]
        direction TB
        ROC_Building["<img src='https://img.icons8.com/ios-filled/50/000000/data-center.png' width='40' /> ROC"]
        ROC_Building --> SCADA["Central Data & SCADA Platform <br/> (Ignition Central Gateway Cluster)"]
        ROC_Building --> MQTT_Broker["MQTT Broker Cluster"]
        ROC_Building --> Historian["Data Historian & Analytics <br/> (PostgreSQL/TimescaleDB, AI/ML Servers)"]
        ROC_Building --> FileStore["File Storage & Management <br/> (FileCatalyst Server)"]
        ROC_Building --> iOPS_UI["Meridian iOPS - User Interface <br/> (Surveyor Access)"]
        iOPS_UI -.->|"Remote Monitoring, Control, <br/> Planning, Analysis & Reporting"| ROC_Building
    end

    %% Connections
    EdgeSys -- "MQTT, File Transfer, Video Feeds" --> CommsLink
    CommsLink -- "MQTT, File Transfer, Video Feeds" --> SCADA
    CommsLink -- "MQTT, File Transfer, Video Feeds" --> MQTT_Broker
    CommsLink -- "MQTT, File Transfer, Video Feeds" --> FileStore
    SCADA --> iOPS_UI
    Historian --> iOPS_UI
    MQTT_Broker --> SCADA

    %% Remote Architecture Highlights
    style Z1 fill:#D1E8FF,stroke:#333,stroke-width:2px
    style Z3 fill:#E8FFD1,stroke:#333,stroke-width:2px
    style CommsLink fill:#FFF3D1,stroke:#333,stroke-width:2px

    %% Bidirectional arrows for commands
    iOPS_UI -- "Commands" --> SCADA
    SCADA -- "Commands" --> CommsLink
    CommsLink -- "Commands" --> EdgeSys

    classDef zoneLabel fill:#f9f,stroke:#333,stroke-width:4px;
    class Z1,Z2,Z3 zoneLabel;
```mermaid
---
title: II. Physical Architecture Diagram - Project Meridian Infrastructure
---
graph LR
    subgraph Vessel [Survey Vessel (Example)]
        direction TB
        subgraph Sensors_V [Sensors]
            MBES_V[<img src='[https://img.icons8.com/ios/50/sonar.png](https://img.icons8.com/ios/50/sonar.png)' width='30' /> MBES]
            INS_GNSS_V[<img src='[https://img.icons8.com/ios/50/gps-antenna.png](https://img.icons8.com/ios/50/gps-antenna.png)' width='30' /> INS/GNSS]
            SVS_V[<img src='[https://img.icons8.com/ios/50/water-temperature.png](https://img.icons8.com/ios/50/water-temperature.png)' width='30' /> SVS]
            MMO_Cam_V[<img src='[https://img.icons8.com/ios/50/security-camera.png](https://img.icons8.com/ios/50/security-camera.png)' width='30' /> MMO Cameras]
            Hydrophones_V[<img src='[https://img.icons8.com/ios/50/microphone.png](https://img.icons8.com/ios/50/microphone.png)' width='30' /> Hydrophones]
            Weather_V[<img src='[https://img.icons8.com/ios/50/barometer.png](https://img.icons8.com/ios/50/barometer.png)' width='30' /> Weather Station]
        end

        subgraph Interface_V [Interface Hardware]
            Moxa_V[Moxa NPort]
        end

        subgraph Network_V [Vessel Network]
            Switch_V[<img src='[https://img.icons8.com/ios/50/network-switch.png](https://img.icons8.com/ios/50/network-switch.png)' width='30' /> PTP Switch]
        end

        subgraph EdgeCompute_V [Edge Compute Units]
            IPC1_Ignition[IPC1: Ignition Edge (Primary)]
            IPC2_MMO_AI[IPC2: MMO Vision AI (GPU)]
            IPC3_PAM_AI[IPC3: PAM Acoustic AI]
        end

        TimeSync_V[<img src='[https://img.icons8.com/ios/50/clock--v1.png](https://img.icons8.com/ios/50/clock--v1.png)' width='30' /> Meinberg LANTIME]

        subgraph CommsGear_V [Comms Gear]
            Starlink_V[<img src='[https://img.icons8.com/ios/50/satellite-dish.png](https://img.icons8.com/ios/50/satellite-dish.png)' width='30' /> Starlink]
            Satcom_V[<img src='[https://img.icons8.com/ios/50/satellite-sending-signal.png](https://img.icons8.com/ios/50/satellite-sending-signal.png)' width='30' /> Satcom]
            Cellular_V[<img src='[https://img.icons8.com/ios/50/cell-tower.png](https://img.icons8.com/ios/50/cell-tower.png)' width='30' /> 4G/5G Modem]
            Router_V[<img src='[https://img.icons8.com/ios/50/router.png](https://img.icons8.com/ios/50/router.png)' width='30' /> Router]
        end

        %% Vessel Connections
        Sensors_V -- Serial --> Moxa_V
        Moxa_V -- Ethernet --> Switch_V
        IPC1_Ignition -- Ethernet --> Switch_V
        IPC2_MMO_AI -- Ethernet --> Switch_V
        IPC3_PAM_AI -- Ethernet --> Switch_V
        MMO_Cam_V -- Ethernet/Video --> IPC2_MMO_AI
        Hydrophones_V -- Analog/Digital --> IPC3_PAM_AI
        TimeSync_V -- Ethernet --> Switch_V
        Switch_V -- Ethernet --> Router_V
        Router_V --> Starlink_V
        Router_V --> Satcom_V
        Router_V --> Cellular_V
    end

    subgraph CommsLink_P [Communication Pathway]
        VSAT_Link[VSAT/Starlink/Cellular Links]
    end

    subgraph ROC_P [Shore-Side Remote Operations Center (ROC)]
        direction TB
        Firewall_ROC[<img src='[https://img.icons8.com/ios/50/firewall.png](https://img.icons8.com/ios/50/firewall.png)' width='30' /> Firewall/Router]
        
        subgraph MQTT_Cluster_ROC [MQTT Broker Cluster]
            MQTT1[MQTT Srv1]
            MQTT2[MQTT Srv2]
            MQTT3[MQTT Srv3]
        end

        subgraph Ignition_Cluster_ROC [Ignition Gateway Cluster]
            IgnitionGW1[Ignition GW1]
            IgnitionGW2[Ignition GW2]
        end

        subgraph DB_Cluster_ROC [Database Cluster]
            DB_PG1[PostgreSQL Srv1]
            DB_PG2[PostgreSQL Srv2]
        end

        subgraph App_Servers_ROC [Application Servers]
            AI_ML_Srv[AI/ML Server]
            GIS_Srv[GIS Server]
            FileCatalyst_Srv[FileCatalyst Server]
        end

        Storage_ROC[<img src='[https://img.icons8.com/ios/50/database.png](https://img.icons8.com/ios/50/database.png)' width='30' /> SAN/NAS Storage]
        Workstations_ROC[<img src='[https://img.icons8.com/ios/50/workstation.png](https://img.icons8.com/ios/50/workstation.png)' width='30' /> Operator Workstations <br/> (Meridian iOPS)]

        %% ROC Connections
        Firewall_ROC --> MQTT_Cluster_ROC
        Firewall_ROC --> Ignition_Cluster_ROC
        Ignition_Cluster_ROC --> DB_Cluster_ROC
        Ignition_Cluster_ROC --> App_Servers_ROC
        DB_Cluster_ROC --> Storage_ROC
        App_Servers_ROC --> Storage_ROC
        Ignition_Cluster_ROC --> Workstations_ROC
        MQTT_Cluster_ROC --> Ignition_Cluster_ROC
    end

    %% Main Link
    CommsGear_V --> VSAT_Link
    VSAT_Link --> Firewall_ROC

    %% Highlighting Remote Architecture
    style Vessel fill:#D1E8FF,stroke:#333,stroke-width:2px
    style ROC_P fill:#E8FFD1,stroke:#333,stroke-width:2px
    style CommsLink_P fill:#FFF3D1,stroke:#333,stroke-width:2px
```mermaid
---
title: III. Remote Operations Focus Diagram - Meridian iOPS Interaction Model
---
graph LR
    subgraph ShoreSide [Shore-Side Systems]
        direction TB
        iOPS_UI[<img src='https://img.icons8.com/ios/50/imac.png' width='40' /> Meridian iOPS <br/> (Web Interface)]
        CentralIgnitionGW[<img src='https://img.icons8.com/color/48/inductive-automation-ignition.png' width='30' /> Central Ignition Gateway <br/> (MQTT Engine, Perspective)]
        MQTT_Broker_S[<img src='https://img.icons8.com/ios/50/data-transfer.png' width='30' /> MQTT Broker Cluster]
        Historian_S[<img src='https://img.icons8.com/ios/50/database.png' width='30' /> Data Historian]
        AUV_Planner_S[AUV Mission Planner Engine]
        LLM_Assistant_S[LLM Assistant]
        FileCatalyst_Srv_S[FileCatalyst Server (Shore)]
        DataStorage_S[Data Storage]
        VideoStreaming_S[Video Streaming Solution (Shore)]

        iOPS_UI -->|User Interaction| CentralIgnitionGW
        CentralIgnitionGW -->|Subscribes/Publishes| MQTT_Broker_S
        MQTT_Broker_S -->|Data| CentralIgnitionGW
        CentralIgnitionGW -->|Stores/Retrieves| Historian_S
        Historian_S -->|Trends| iOPS_UI
        AUV_Planner_S -->|Plans| iOPS_UI
        LLM_Assistant_S -->|Assistance| iOPS_UI
        iOPS_UI -->|Access| FileCatalyst_Srv_S
        FileCatalyst_Srv_S --> DataStorage_S
        VideoStreaming_S -->|Live Feeds| iOPS_UI
    end

    subgraph CommsLink_IO [Communication Link]
        direction TB
        Link[<img src='https://img.icons8.com/ios/50/000000/satellite-dish.png' width='30' /> <img src='https://img.icons8.com/ios/50/000000/cell-tower.png' width='30' />]
    end
    
    subgraph VesselSide [Vessel Edge Systems]
        direction TB
        EdgeIgnition[<img src='https://img.icons8.com/color/48/inductive-automation-ignition-edge.png' width='30' /> Vessel Edge <br/> (Ignition Edge - Sparkplug B)]
        MMO_AI_Detect[MMO AI Detections]
        PAM_AI_Detect[PAM AI Detections]
        LocalRawData[Local Raw Data Log]
        VideoFeeds_V[Video Streams (MMO Cams)]
        FileCatalyst_Agent_V[FileCatalyst Agent (Vessel)]
        TargetSensorSystem[Target Sensor/System]

        EdgeIgnition -->|Sparkplug B Data| Link
        MMO_AI_Detect -->|Detections| EdgeIgnition
        PAM_AI_Detect -->|Detections| EdgeIgnition
        VideoFeeds_V -->|Streams| Link
        LocalRawData --> FileCatalyst_Agent_V
        FileCatalyst_Agent_V -->|On-demand Transfer| Link
    end

    %% Data Flow (Vessel to Shore)
    Link -->|Sparkplug B Data| MQTT_Broker_S
    Link -->|Video Streams| VideoStreaming_S
    Link -->|File Transfer| FileCatalyst_Srv_S
    
    %% Command Flow (Shore to Vessel)
    MQTT_Broker_S -- "DCMD/NCMD Sparkplug B" --> Link
    Link -- "DCMD/NCMD Sparkplug B" --> EdgeIgnition
    EdgeIgnition -- "Scripting/Device Control" --> TargetSensorSystem

    %% Highlighting
    style ShoreSide fill:#E8FFD1,stroke:#333,stroke-width:2px
    style VesselSide fill:#D1E8FF,stroke:#333,stroke-width:2px
    style CommsLink_IO fill:#FFF3D1,stroke:#333,stroke-width:2px
```mermaid
---
title: IV. Data Flow Diagram - MBES Data to Meridian iOPS
---
graph LR
    subgraph VesselHardware [Vessel Hardware]
        MBES[Kongsberg EM2040 MBES <br/> (.all, .kmall datagrams)]
        VesselNet_DF[Vessel Network]
        MBES --> VesselNet_DF
    end

    subgraph VesselEdgeSoftware [Vessel Edge Software (Ignition Edge)]
        EdgeCompute[Edge Compute Unit]
        EdgeInput[Input: MBES Datagrams]
        EdgeProcessing[Processing: <br/> - Extract Status, QC, Nav <br/> - Log Raw Data Locally]
        EdgeOutput[Output: Sparkplug B <br/> (MBES_Status, MBES_QC)]
        FileAgent_DF[FileCatalyst Agent]

        VesselNet_DF --> EdgeInput
        EdgeInput --> EdgeProcessing
        EdgeProcessing --> EdgeOutput
        EdgeProcessing --> FileAgent_DF
    end

    subgraph Comms_DF [Communication Link]
        MQTT_Link_DF[MQTT (Sparkplug B)]
        FileTransfer_Link_DF[File Transfer (FileCatalyst)]
        EdgeOutput --> MQTT_Link_DF
        FileAgent_DF -.->|"On-Demand Raw Data"| FileTransfer_Link_DF
    end
    
    subgraph ShoreBroker_DF [Shore-Side MQTT Broker]
        MQTT_Broker_Cluster_DF[MQTT Broker Cluster]
        MQTT_Link_DF --> MQTT_Broker_Cluster_DF
    end

    subgraph ShoreGatewaySoftware [Shore-Side Central Ignition Gateway]
        CentralGW_DF[Central Ignition Gateway]
        MQTTEngine_DF[MQTT Engine <br/> (Subscribes MBES Topics)]
        Historian_DF[Historian <br/> (Logs MBES QC/Status)]
        Perspective_DF[Perspective <br/> (Serves Data to iOPS)]
        
        MQTT_Broker_Cluster_DF --> MQTTEngine_DF
        MQTTEngine_DF --> Historian_DF
        MQTTEngine_DF --> Perspective_DF
    end

    subgraph Meridian_iOPS_DF [Meridian iOPS Display]
        iOPS_Display[Meridian iOPS Display <br/> - Real-time Status, QC Plots <br/> - Coverage Map, Alerts]
        RawDataRequest[Raw Data Request <br/> (User Action)]

        Perspective_DF --> iOPS_Display
        iOPS_Display -.-> RawDataRequest
    end
    
    subgraph ShoreFileServer_DF [Shore File Server]
        FileServer_DF[FileCatalyst Server (Shore)]
        FileTransfer_Link_DF --> FileServer_DF
    end

    %% Raw Data Request Flow (Simplified)
    RawDataRequest -.->|"Command via MQTT"| EdgeProcessing
    
    %% Highlighting Remote Architecture
    style VesselHardware fill:#DDEBF7,stroke:#333,stroke-width:1px
    style VesselEdgeSoftware fill:#D1E8FF,stroke:#333,stroke-width:2px
    style Comms_DF fill:#FFF3D1,stroke:#333,stroke-width:2px
    style ShoreBroker_DF fill:#E2F0D9,stroke:#333,stroke-width:1px
    style ShoreGatewaySoftware fill:#C9EAD4,stroke:#333,stroke-width:2px
    style Meridian_iOPS_DF fill:#A9D18E,stroke:#333,stroke-width:2px
    style ShoreFileServer_DF fill:#E2F0D9,stroke:#333,stroke-width:1px

    classDef laneLabel fill:#f9f,stroke:#333,stroke-width:4px;
```mermaid
---
title: V. Ignition Architecture Diagram - Project Meridian Focus
---
graph TD
    subgraph Fleet [Fleet of Vessels (Multiple Ignition Edge Gateways)]
        direction LR
        EdgeGW1[<img src='https://img.icons8.com/color/48/inductive-automation-ignition-edge.png' width='40' /> Vessel 1 Edge GW <br/> MQTT Tx (Sparkplug B) <br/> Local Drivers, Scripting <br/> EAM Agent]
        EdgeGW2[<img src='https://img.icons8.com/color/48/inductive-automation-ignition-edge.png' width='40' /> Vessel 2 Edge GW <br/> MQTT Tx (Sparkplug B) <br/> Local Drivers, Scripting <br/> EAM Agent]
        EdgeGW_N[<img src='https://img.icons8.com/color/48/inductive-automation-ignition-edge.png' width='40' /> Vessel N Edge GW <br/> ... ]
    end

    subgraph ShoreMQTT [Shore-Side MQTT Broker Cluster]
        MQTT_Broker_Ign[<img src='https://img.icons8.com/ios/50/data-transfer.png' width='40' /> MQTT Broker Cluster]
    end

    subgraph ShoreIgnition [Shore-Side Central Ignition Gateway Cluster]
        direction TB
        CentralGW_Cluster[<img src='https://img.icons8.com/color/48/inductive-automation-ignition.png' width='50' /> Central Ignition Gateway Cluster <br/> (Primary & Backup)]
        
        subgraph Modules [Key Ignition Modules]
            MQTTEngine[MQTT Engine]
            PerspectiveMod[Perspective Module]
            HistorianMod[Historian Module]
            ReportingMod[Reporting Module]
            AlarmMod[Alarm Notification Module]
            EAM_Mod[Enterprise Admin Module (EAM)]
            ScriptingEng[Scripting Engine]
        end
        CentralGW_Cluster --> Modules
    end

    subgraph ShoreDB [Database Cluster]
        DB_Cluster_Ign[<img src='https://img.icons8.com/ios/50/database.png' width='40' /> PostgreSQL/TimescaleDB Cluster]
    end
    
    subgraph Users [Meridian iOPS Users]
        UserDisplay[<img src='https://img.icons8.com/ios/50/imac.png' width='40' /> Web Browsers]
    end

    %% Connections
    EdgeGW1 -- "Sparkplug B Data" --> ShoreMQTT
    EdgeGW2 -- "Sparkplug B Data" --> ShoreMQTT
    EdgeGW_N -- "Sparkplug B Data" --> ShoreMQTT
    
    ShoreMQTT -- "Sparkplug B Data" --> MQTTEngine

    MQTTEngine --> CentralGW_Cluster
    PerspectiveMod --> UserDisplay
    HistorianMod -- "Stores/Retrieves" --> ShoreDB
    EAM_Mod -- "Manages/Updates" --> EdgeGW1
    EAM_Mod -- "Manages/Updates" --> EdgeGW2
    EAM_Mod -- "Manages/Updates" --> EdgeGW_N
    
    %% Highlighting Remote Architecture
    style Fleet fill:#D1E8FF,stroke:#333,stroke-width:2px
    style ShoreMQTT fill:#FFF3D1,stroke:#333,stroke-width:2px
    style ShoreIgnition fill:#E8FFD1,stroke:#333,stroke-width:2px
    style ShoreDB fill:#E8FFD1,stroke:#333,stroke-width:1px
    style Users fill:#E8FFD1,stroke:#333,stroke-width:1px
